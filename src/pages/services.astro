---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Services">
	<!-- Services Hero Section -->
	<section class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-20 overflow-hidden transform transition-all duration-500 hover:scale-[1.02]">
		<div class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-10 animate-pulse"></div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div class="text-center">
				<div class="flex justify-center">
					<img
						src="/logo-nobg-white-stroke.png"
						alt="Lugetech Logo"
						class="h-40 w-40"
					/>
				</div>
				<h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl animate-fade-in">
					<span class="block bg-clip-text text-transparent bg-gradient-to-r from-white to-indigo-200">Tech Services by Lugetech Guyana</span>
				</h1>
				<p class="mt-3 max-w-md mx-auto text-base text-indigo-200 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl animate-fade-in-delayed">
					Comprehensive technology solutions tailored to your needs
				</p>
			</div>
		</div>
	</section>

	<!-- Services Grid Section -->
	<section class="py-16 bg-gray-800">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="grid grid-cols-1 gap-12 lg:grid-cols-2">
				<!-- Custom Software Development -->
				<div class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gray-600 border border-gray-600 hover:border-indigo-500 relative overflow-hidden">
					<div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
					<div class="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 transform transition-transform duration-500 group-hover:rotate-12 group-hover:scale-110">
						<svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-white mb-4 relative z-10 transition-colors duration-300 group-hover:text-indigo-300">Custom Software Development</h3>
					<p class="text-gray-300 mb-4 relative z-10 transition-colors duration-300 group-hover:text-white">We create tailored software solutions that perfectly align with your business objectives and workflows. Our development process ensures high-quality, scalable, and maintainable applications.</p>
					<ul class="text-gray-300 space-y-2 relative z-10">
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Web Applications</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Mobile Apps</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Enterprise Software</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• API Development</li>
					</ul>
				</div>

				<!-- Cloud Solutions -->
				<div class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gray-600 border border-gray-600 hover:border-indigo-500 relative overflow-hidden">
					<div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
					<div class="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 transform transition-transform duration-500 group-hover:rotate-12 group-hover:scale-110">
						<svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-white mb-4 relative z-10 transition-colors duration-300 group-hover:text-indigo-300">Cloud Solutions</h3>
					<p class="text-gray-300 mb-4 relative z-10 transition-colors duration-300 group-hover:text-white">Leverage the power of cloud computing with our comprehensive cloud solutions. We help you migrate, optimize, and manage your cloud infrastructure for maximum efficiency.</p>
					<ul class="text-gray-300 space-y-2 relative z-10">
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Cloud Migration</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Infrastructure as Code</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Cloud Security</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• DevOps Services</li>
					</ul>
				</div>

				<!-- Digital Transformation -->
				<div class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gray-600 border border-gray-600 hover:border-indigo-500 relative overflow-hidden">
					<div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
					<div class="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 transform transition-transform duration-500 group-hover:rotate-12 group-hover:scale-110">
						<svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-white mb-4 relative z-10 transition-colors duration-300 group-hover:text-indigo-300">Digital Transformation</h3>
					<p class="text-gray-300 mb-4 relative z-10 transition-colors duration-300 group-hover:text-white">Transform your business with cutting-edge digital solutions. We help you modernize your processes and create new opportunities through technology.</p>
					<ul class="text-gray-300 space-y-2 relative z-10">
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Process Automation</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Digital Strategy</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Legacy System Modernization</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Data Analytics</li>
					</ul>
				</div>

				<!-- Consulting Services -->
				<div class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gray-600 border border-gray-600 hover:border-indigo-500 relative overflow-hidden">
					<div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
					<div class="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 transform transition-transform duration-500 group-hover:rotate-12 group-hover:scale-110">
						<svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-white mb-4 relative z-10 transition-colors duration-300 group-hover:text-indigo-300">Consulting Services</h3>
					<p class="text-gray-300 mb-4 relative z-10 transition-colors duration-300 group-hover:text-white">Get expert guidance on your technology initiatives. Our consultants help you make informed decisions and implement effective solutions.</p>
					<ul class="text-gray-300 space-y-2 relative z-10">
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Technology Assessment</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Solution Architecture</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Security Consulting</li>
						<li class="transform transition-transform duration-300 hover:translate-x-2">• Project Management</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-16 transform hover:scale-[1.02] transition-all duration-500 relative overflow-hidden">
		<div class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-5"></div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div class="text-center">
				<h2 class="text-3xl font-extrabold text-white sm:text-4xl mb-8">
					Ready to transform your business?
				</h2>
				<a href="/contact" class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transform hover:scale-105 transition-all duration-300 shadow-lg">
						Get Started
					</a>
				</div>
			</div>
		</div>
	</section>
</Layout>

<style>
@keyframes fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fade-in 0.8s ease-out 0.2s forwards;
  opacity: 0;
}
</style>