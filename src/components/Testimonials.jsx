import React, { useState, useEffect } from 'react';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'CEO, TechCorp',
    content: 'Lugetech transformed our business with their innovative solutions. Their team\'s expertise and dedication exceeded our expectations.',
    image: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'CTO, InnovateCo',
    content: 'Working with Lugetech was a game-changer for our digital transformation journey. Their custom software solutions are top-notch.',
    image: 'https://i.pravatar.cc/150?img=5'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Director, FutureTech',
    content: 'The cloud solutions provided by Lugetech helped us scale our operations efficiently. Highly recommended!',
    image: 'https://i.pravatar.cc/150?img=8'
  }
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    if (isPaused) return;
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [isPaused]);

  return (
    <div
      className="relative overflow-hidden bg-gray-900 py-24"
      role="region"
      aria-label="Testimonials"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      onFocus={() => setIsPaused(true)}
      onBlur={() => setIsPaused(false)}
      tabIndex="0"
    >
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            What Our Clients Say
          </h2>
          <p className="mt-6 text-xl text-gray-300">
            Don't just take our word for it
          </p>
        </div>

        <div className="mt-16">
          <div className="relative min-h-[300px]" aria-live="polite">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={`transition-all duration-500 absolute inset-0 flex items-center justify-center ${index === currentIndex ? 'opacity-100 translate-y-0 z-10' : 'opacity-0 translate-y-4 z-0'}`}
              >
                <div className="max-w-3xl mx-auto text-center relative">
                  <div className="mb-16">
                    <img
                      className="mx-auto h-24 w-24 rounded-full border-4 border-indigo-500 shadow-lg"
                      src={testimonial.image}
                      alt={testimonial.name}
                    />
                  </div>
                  <blockquote>
                    <p className="text-2xl font-medium text-white mb-8 leading-relaxed">
                      "{testimonial.content}"
                    </p>
                    <footer className="text-base font-medium text-indigo-400">
                      {testimonial.name}
                      <span className="block text-gray-400 text-sm mt-1">
                        {testimonial.role}
                      </span>
                    </footer>
                  </blockquote>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 flex justify-center space-x-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`h-3 w-3 rounded-full transition-colors duration-200 ${index === currentIndex ? 'bg-indigo-500' : 'bg-gray-600'}`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
