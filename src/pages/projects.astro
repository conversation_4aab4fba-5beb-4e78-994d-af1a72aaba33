---
import Layout from "../layouts/Layout.astro";
import ProjectGrid from "../components/ProjectGrid.astro"; // Import the new component
import type { EmailJSResponseStatus } from "@emailjs/browser";
import emailjs from "@emailjs/browser";

// Define project data
const projects = [
	{
		imgSrc: "/products/reviewit.png",
		imgAlt: "reviewit",
		title: "Reviewit",
		description:
			"ReviewIt is a powerful online review platform that enables users to share their experiences and opinions about various products and services.",
		tags: ["NextJS", "Go", "Postgresql"],
	},
	{
		imgSrc: "/products/vault.png",
		imgAlt: "vaultgy",
		title: "Vaultgy",
		description:
			"Vaultgy is a comprehensive subscription management platform that allows merchants to easily embed and sell subscription products on their websites.",
		tags: ["Astro / Svelte 5", "Go", "PostgreSQL"],
	},
	{
		imgSrc: "/products/scangy.png",
		imgAlt: "scangy",
		title: "ScanGY",
		description:
			"ScanGY is a web application that provides URL shortening and QR code generation services. It consists of a front-end React application and a back-end API server written in Go.",
		tags: ["NextJS", "Go", "MongoDB"],
	},
];
---

<Layout title="Projects">
	<!-- Projects Hero Section -->
	<section
		class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-20 overflow-hidden transform transition-all duration-500 hover:scale-[1.02]"
	>
		<div
			class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-10 animate-pulse"
		>
		</div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div class="flex flex-col w-full items-center justify-center">
				<img
					src="/logo-nobg-white-stroke.png"
					alt="Lugetech Logo"
					class="h-40 w-40"
				/>
				<h1
					class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl animate-fade-in"
				>
					<span
						class="block bg-clip-text text-transparent bg-gradient-to-r from-white to-indigo-200"
						>Innovative Tech Projects by Lugetech Guyana</span
					>
				</h1>
				<p
					class="mt-3 max-w-md mx-auto text-base text-indigo-200 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl animate-fade-in-delayed"
				>
					Explore our portfolio of successful digital transformations
				</p>
			</div>
		</div>
	</section>

	<!-- Use the new ProjectGrid component -->
	<!-- <ProjectGrid projects={projects} /> -->

	<!-- CTA Section -->
	<section
		class="bg-gray-900 transform hover:scale-[1.02] transition-all duration-500"
	>
		<div
			class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8 lg:flex lg:items-center lg:justify-between relative z-10"
		>
			<div
				class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-5"
			>
			</div>
			<h2
				class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl"
			>
				<span class="block">Have a project in mind?</span>
				<span class="block text-indigo-200 text-2xl mt-2"
					>Let's discuss how we can help bring your vision to life.</span
				>
			</h2>
			<div class="mt-8 flex lg:mt-0 lg:flex-shrink-0">
				<div class="inline-flex rounded-md shadow">
					<a
						href="#start-project"
						class="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transform hover:scale-105 transition-all duration-300"
					>
						Start a Project
					</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Project Form Section -->
	<section id="start-project" class="py-16 bg-gray-900">
		<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
			<div
				class="bg-gray-800 p-8 rounded-xl shadow-lg border border-gray-600 transform transition-all duration-500 hover:scale-[1.01] hover:border-indigo-500"
			>
				<h2 class="text-3xl font-bold text-white mb-8 text-center">
					Start Your Project
				</h2>
				<form id="projectForm" class="space-y-6">
					<div class="space-y-2">
						<label
							for="project_name"
							class="block text-sm font-medium text-indigo-200"
							>Project Name</label
						>
						<input
							type="text"
							id="project_name"
							name="project_name"
							class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
							placeholder="Enter your project name"
							required
						/>
					</div>

					<div class="space-y-2">
						<label
							for="project_type"
							class="block text-sm font-medium text-indigo-200"
							>Project Type</label
						>
						<select
							id="project_type"
							name="project_type"
							class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
							required
						>
							<option value="">Select a project type</option>
							<option value="Web Development"
								>Web Development</option
							>
							<option value="Mobile App">Mobile App</option>
							<option value="E-commerce">E-commerce</option>
							<option value="Custom Software"
								>Custom Software</option
							>
							<option value="Other">Other</option>
						</select>
					</div>

					<div class="space-y-2">
						<label
							for="project_description"
							class="block text-sm font-medium text-indigo-200"
							>Project Description</label
						>
						<textarea
							id="project_description"
							name="project_description"
							rows="4"
							class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
							placeholder="Describe your project requirements"
							required></textarea>
					</div>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div class="space-y-2">
							<label
								for="expected_timeline"
								class="block text-sm font-medium text-indigo-200"
								>Expected Timeline</label
							>
							<select
								id="expected_timeline"
								name="expected_timeline"
								class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
								required
							>
								<option value="">Select timeline</option>
								<option value="1-3 months">1-3 months</option>
								<option value="3-6 months">3-6 months</option>
								<option value="6-12 months">6-12 months</option>
								<option value="12+ months">12+ months</option>
							</select>
						</div>

						<div class="space-y-2">
							<label
								for="budget_range"
								class="block text-sm font-medium text-indigo-200"
								>Budget Range</label
							>
							<select
								id="budget_range"
								name="budget_range"
								class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
								required
							>
								<option value="">Select budget range</option>
								<option value="Below $5,000"
									>Below $5,000</option
								>
								<option value="$5,000 - $10,000"
									>$5,000 - $10,000</option
								>
								<option value="$10,000 - $25,000"
									>$10,000 - $25,000</option
								>
								<option value="$25,000+">$25,000+</option>
							</select>
						</div>
					</div>

					<div class="space-y-2">
						<label
							for="contact_information"
							class="block text-sm font-medium text-indigo-200"
							>Contact Information</label
						>
						<input
							type="email"
							id="contact_information"
							name="contact_information"
							class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
							placeholder="Your email address"
							required
						/>
					</div>

					<input type="hidden" name="time" id="time" />

					<div class="pt-4">
						<button
							type="submit"
							id="submitButton"
							class="w-full flex justify-center py-4 px-6 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:scale-[1.02] transition-all duration-300"
						>
							Submit Project Request
						</button>
					</div>

					<div id="formStatus" class="text-center hidden mt-4">
						<p id="successMessage" class="text-green-400 hidden">
							Project request submitted successfully!
						</p>
						<p id="errorMessage" class="text-red-400 hidden">
							Error submitting request. Please try again.
						</p>
					</div>
				</form>
			</div>
		</div>
	</section>

	<script>
		// Use client-side import
		import("@emailjs/browser").then((emailjs) => {
			const form = document.getElementById(
				"projectForm",
			) as HTMLFormElement;
			const submitButton = document.getElementById(
				"submitButton",
			) as HTMLButtonElement;
			const formStatus = document.getElementById(
				"formStatus",
			) as HTMLDivElement;
			const successMessage = document.getElementById(
				"successMessage",
			) as HTMLParagraphElement;
			const errorMessage = document.getElementById(
				"errorMessage",
			) as HTMLParagraphElement;
			const timeInput = document.getElementById(
				"time",
			) as HTMLInputElement;

			// Form validation
			const validateForm = () => {
				const projectName = (
					document.getElementById("project_name") as HTMLInputElement
				).value;
				const projectDescription = (
					document.getElementById(
						"project_description",
					) as HTMLTextAreaElement
				).value;
				const email = (
					document.getElementById(
						"contact_information",
					) as HTMLInputElement
				).value;

				if (projectName.length < 3) {
					throw new Error(
						"Project name must be at least 3 characters long",
					);
				}

				if (projectDescription.length < 50) {
					throw new Error(
						"Please provide a more detailed project description (at least 50 characters)",
					);
				}

				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
				if (!emailRegex.test(email)) {
					throw new Error("Please enter a valid email address");
				}

				return true;
			};

			// Initialize EmailJS
			emailjs.init({
				publicKey: import.meta.env.PUBLIC_EMAILJS_PUBLIC_KEY || "",
			});

			if (form) {
				form.addEventListener("submit", async (e) => {
					e.preventDefault();

					// Disable submit button and show loading state
					submitButton.disabled = true;
					submitButton.innerHTML = "Sending...";

					// Hide any previous status messages
					formStatus.classList.add("hidden");
					successMessage.classList.add("hidden");
					errorMessage.classList.add("hidden");

					try {
						// Validate form
						validateForm();

						// Set current time
						timeInput.value = new Date().toLocaleString();

						await emailjs.sendForm(
							import.meta.env.PUBLIC_EMAILJS_SERVICE_ID || "",
							import.meta.env
								.PUBLIC_EMAILJS_PROJECT_TEMPLATE_ID || "",
							form,
						);

						// Show success message
						formStatus.classList.remove("hidden");
						successMessage.classList.remove("hidden");
						form.reset();
					} catch (error) {
						// Show error message
						console.error("Error:", error);
						formStatus.classList.remove("hidden");
						errorMessage.textContent =
							error instanceof Error
								? error.message
								: "Error submitting request. Please try again.";
						errorMessage.classList.remove("hidden");
					} finally {
						// Reset button state
						submitButton.disabled = false;
						submitButton.innerHTML = "Submit Project Request";
					}
				});
			}
		});
	</script>

	<style>
		@keyframes fade-in {
			from {
				opacity: 0;
				transform: translateY(20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		.animate-fade-in {
			animation: fade-in 0.8s ease-out forwards;
		}

		.animate-fade-in-delayed {
			animation: fade-in 0.8s ease-out 0.2s forwards;
			opacity: 0;
		}

		html {
			scroll-behavior: smooth;
		}
	</style>
</Layout>
