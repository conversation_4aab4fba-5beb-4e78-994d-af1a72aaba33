# Project Context

## Dev & Build
- `npm run dev` / `pnpm dev`: start Astro dev server
- `npm run build`: build production site
- `npm run preview`: preview built site locally
- `tsc --noEmit`: run TypeScript type checking

## Lint & Test
- Lint: `npx eslint src/` & `npx prettier --check .`
- Testing: none configured; future use `npm test` (all) or `npm test -- <file>` / `vitest run <file>` (single)

## Code Style
- Imports: ESM, absolute from `src/` or relative (`./`, `../`)
- File naming: PascalCase for components/files; camelCase for variables/functions
- Extensions: `.ts`, `.js`, `.jsx`, `.astro`
- Formatting: Prettier (2-space indent) and Tailwind utilities
- Async: use `async/await` with `try/catch` and clear error messages

## Conventions
- Env variables via `import.meta.env.PUBLIC_*` (see `src/config.ts`)
- Public assets in `public/`; source code in `src/`
- No `.cursor/rules/` or Copilot instructions present
