---
// Define props with TypeScript interface
interface Props {
  as?: string; // HTML element to render (default: 'section')
  delay?: number; // Delay in milliseconds before animation starts
  direction?: 'up' | 'down' | 'left' | 'right' | 'none'; // Direction of the animation
  duration?: number; // Duration of the animation in milliseconds
  class?: string; // Additional CSS classes
}

// Destructure props with defaults
const { 
  as: Element = 'section',
  delay = 0,
  direction = 'up',
  duration = 800,
  class: className = '',
} = Astro.props;

// Calculate transform based on direction
let transform = '';
switch (direction) {
  case 'up':
    transform = 'translateY(30px)';
    break;
  case 'down':
    transform = 'translateY(-30px)';
    break;
  case 'left':
    transform = 'translateX(30px)';
    break;
  case 'right':
    transform = 'translateX(-30px)';
    break;
  default:
    transform = 'none';
}

// Build the style attribute
const style = `
  --animation-duration: ${duration}ms;
  --animation-delay: ${delay}ms;
  --animation-transform: ${transform};
`;
---

<Element 
  class:list={['scroll-animate', className]} 
  style={style}
>
  <slot />
</Element>

<style>
  .scroll-animate {
    opacity: 0;
    transform: var(--animation-transform);
    transition: opacity var(--animation-duration) ease var(--animation-delay),
                transform var(--animation-duration) ease var(--animation-delay);
    will-change: opacity, transform;
  }

  .scroll-animate.scroll-visible {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
</style>
