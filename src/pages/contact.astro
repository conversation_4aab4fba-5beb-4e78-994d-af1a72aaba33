---
import Layout from '../layouts/Layout.astro';
import { EMAILJS_PUBLIC_KEY, EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID } from '../config';
---

<Layout title="Contact">
	<!-- Contact Hero Section -->
	<section class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-20 overflow-hidden transform transition-all duration-500 hover:scale-[1.02]">
		<div class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-10 animate-pulse"></div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div class="text-center">
				<div class="flex justify-center">
					<img
						src="/logo-nobg-white-stroke.png"
						alt="Lugetech Logo"
						class="h-40 w-40"
					/>
				</div>
				<h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl animate-fade-in">
					<span class="block bg-clip-text text-transparent bg-gradient-to-r from-white to-indigo-200">Contact Lugetech - Leading Tech Company in Guyana</span>
				</h1>
				<p class="mt-3 max-w-md mx-auto text-base text-indigo-200 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl animate-fade-in-delayed">
					Let's discuss how we can help transform your business
				</p>
			</div>
		</div>
	</section>

	<!-- Contact Form Section -->
	<section class="py-16 bg-gray-800">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
				<!-- Contact Form -->
				<div class="bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-600 transform transition-all duration-500 hover:scale-[1.01] hover:border-indigo-500">
					<h2 class="text-2xl font-bold text-white mb-6">Send us a Message</h2>
					<form id="contactForm" class="space-y-6">
						<div class="relative group">
							<input type="text" name="name" id="name" class="block w-full px-4 py-3 bg-gray-600 border border-gray-500 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-white placeholder-transparent peer" placeholder="Name" required />
							<label for="name" class="absolute left-4 -top-6 text-sm text-gray-300 transition-all peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-3 peer-focus:-top-6 peer-focus:text-sm peer-focus:text-indigo-400">Name</label>
						</div>
						<div class="relative group">
							<input type="email" name="email" id="email" class="block w-full px-4 py-3 bg-gray-600 border border-gray-500 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-white placeholder-transparent peer" placeholder="Email" required />
							<label for="email" class="absolute left-4 -top-6 text-sm text-gray-300 transition-all peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-3 peer-focus:-top-6 peer-focus:text-sm peer-focus:text-indigo-400">Email</label>
						</div>
						<div class="relative group">
							<textarea name="message" id="message" rows="4" class="block w-full px-4 py-3 bg-gray-600 border border-gray-500 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-white placeholder-transparent peer" placeholder="Message" required></textarea>
							<label for="message" class="absolute left-4 -top-6 text-sm text-gray-300 transition-all peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-3 peer-focus:-top-6 peer-focus:text-sm peer-focus:text-indigo-400">Message</label>
						</div>
						<button type="submit" id="submitButton" class="w-full px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-md shadow-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform transition-all duration-300 hover:scale-[1.02]">
							Send Message
						</button>
						<div id="formStatus" class="text-center hidden">
							<p id="successMessage" class="text-green-400 hidden">Message sent successfully!</p>
							<p id="errorMessage" class="text-red-400 hidden">Error sending message. Please try again.</p>
						</div>
					</form>
				</div>

				<!-- Contact Information -->
				<div class="space-y-8">
					<div class="bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-600 transform transition-all duration-500 hover:scale-[1.01] hover:border-indigo-500">
						<h3 class="text-xl font-bold text-white mb-4">Location</h3>
						<p class="text-gray-300">Georgetown, Guyana</p>
					</div>

					<div class="bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-600 transform transition-all duration-500 hover:scale-[1.01] hover:border-indigo-500">
						<h3 class="text-xl font-bold text-white mb-4">Contact Details</h3>
						<div class="space-y-4">
							<p class="text-gray-300">
								<span class="block font-medium text-indigo-400">Email:</span>
								<EMAIL>
							</p>
						</div>
					</div>

					<div class="bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-600 transform transition-all duration-500 hover:scale-[1.01] hover:border-indigo-500">
						<h3 class="text-xl font-bold text-white mb-4">Business Hours</h3>
						<div class="space-y-2 text-gray-300">
							<p>Monday - Friday: 9:00 AM - 6:00 PM</p>
							<p>Saturday: 10:00 AM - 2:00 PM</p>
							<p>Sunday: Closed</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	import emailjs from '@emailjs/browser';

	const form = document.getElementById('contactForm') as HTMLFormElement;
	const submitButton = document.getElementById('submitButton') as HTMLButtonElement;
	const formStatus = document.getElementById('formStatus') as HTMLDivElement;
	const successMessage = document.getElementById('successMessage') as HTMLParagraphElement;
	const errorMessage = document.getElementById('errorMessage') as HTMLParagraphElement;

	// Initialize EmailJS
	emailjs.init({
		publicKey: import.meta.env.PUBLIC_EMAILJS_PUBLIC_KEY || '',
	});

	if (form) {
		form.addEventListener('submit', async (e) => {
			e.preventDefault();

			// Disable submit button and show loading state
			submitButton.disabled = true;
			submitButton.innerHTML = 'Sending...';

			// Hide any previous status messages
			formStatus.classList.add('hidden');
			successMessage.classList.add('hidden');
			errorMessage.classList.add('hidden');

			try {
				await emailjs.sendForm(
					import.meta.env.PUBLIC_EMAILJS_SERVICE_ID || '',
					import.meta.env.PUBLIC_EMAILJS_TEMPLATE_ID || '',
					form,
				);

				// Show success message
				formStatus.classList.remove('hidden');
				successMessage.classList.remove('hidden');
				form.reset();
			} catch (error) {
				// Show error message
				console.error('Error sending email:', error);
				formStatus.classList.remove('hidden');
				errorMessage.classList.remove('hidden');
			} finally {
				// Reset button state
				submitButton.disabled = false;
				submitButton.innerHTML = 'Send Message';
			}
		});
	}
</script>

<style>
@keyframes fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fade-in 0.8s ease-out 0.2s forwards;
  opacity: 0;
}
</style>