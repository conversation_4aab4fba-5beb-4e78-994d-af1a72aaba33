---
import { Image } from 'astro:assets';
// src/components/ProjectGrid.astro
interface Project {
	imgSrc: string;
	imgAlt: string;
	title: string;
	description: string;
	tags: string[];
}

export interface Props {
	projects: Project[];
}

const { projects } = Astro.props;
---

<section class="py-16 bg-gray-800" role="region" aria-label="Projects">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-3">
			{
				projects.map((project) => (
					<article
					  class="group bg-gray-700 rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:scale-105 hover:shadow-2xl border border-gray-600 hover:border-indigo-500"
					  tabindex="0"
					  aria-labelledby={"project-title-" + project.title.replace(/\s+/g, '-').toLowerCase()}
					>
						<div class="relative h-64 w-full overflow-hidden">
							<Image
								src={project.imgSrc}
								alt={project.imgAlt}
								class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
							/>
							<div class="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60" />
						</div>
						<div class="p-6">
							<h3
							  id={"project-title-" + project.title.replace(/\s+/g, '-').toLowerCase()}
							  class="text-xl font-bold text-white mb-2 group-hover:text-indigo-400 transition-colors duration-300"
							>
								{project.title}
							</h3>
							<p class="text-gray-300 mb-4">{project.description}</p>
							<div class="flex flex-wrap gap-2">
								{project.tags.map((tag) => (
									<span class="px-3 py-1 text-sm bg-indigo-900 text-indigo-200 rounded-full">
										{tag}
									</span>
								))}
							</div>
						</div>
					</article>
				))
			}
		</div>
	</div>
</section>
