---
import Layout from "../layouts/Layout.astro";
---

<Layout title="About">
	<!-- About Hero Section -->
	<section
		class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-20 overflow-hidden"
	>
		<div
			class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-10 animate-pulse"
		>
		</div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div
				class="text-center transform transition-all duration-500 hover:scale-[1.02]"
			>
				<div class="flex justify-center">
					<img
						src="/logo-nobg-white-stroke.png"
						alt="Lugetech Logo"
						class="h-40 w-40"
					/>
				</div>
				<h1
					class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl animate-fade-in"
				>
					<span
						class="block bg-clip-text text-transparent bg-gradient-to-r from-white to-indigo-200"
						>About Lugetech - Tech Innovation in Guyana</span
					>
				</h1>
				<p
					class="mt-3 max-w-md mx-auto text-base text-indigo-200 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl animate-fade-in-delayed"
				>
					Driving innovation and digital transformation since 2015
				</p>
			</div>
		</div>
	</section>

	<!-- Stats Section -->
	<section class="py-12 bg-gray-900">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
				<div
					class="bg-gray-800 p-6 rounded-xl border border-gray-700 transform hover:scale-105 transition-all duration-300 group hover:bg-gradient-to-br from-indigo-600 to-purple-600"
				>
					<div
						class="text-4xl font-bold text-white mb-2 group-hover:text-indigo-200 transition-colors duration-300"
					>
						10+
					</div>
					<div class="text-gray-400 group-hover:text-indigo-100">
						Years of Excellence
					</div>
				</div>
				<div
					class="bg-gray-800 p-6 rounded-xl border border-gray-700 transform hover:scale-105 transition-all duration-300 group hover:bg-gradient-to-br from-indigo-600 to-purple-600"
				>
					<div
						class="text-4xl font-bold text-white mb-2 group-hover:text-indigo-200 transition-colors duration-300"
					>
						100+
					</div>
					<div class="text-gray-400 group-hover:text-indigo-100">
						Projects Delivered
					</div>
				</div>
				<div
					class="bg-gray-800 p-6 rounded-xl border border-gray-700 transform hover:scale-105 transition-all duration-300 group hover:bg-gradient-to-br from-indigo-600 to-purple-600"
				>
					<div
						class="text-4xl font-bold text-white mb-2 group-hover:text-indigo-200 transition-colors duration-300"
					>
						100%
					</div>
					<div class="text-gray-400 group-hover:text-indigo-100">
						Satisfaction Ratings
					</div>
				</div>
				<div
					class="bg-gray-800 p-6 rounded-xl border border-gray-700 transform hover:scale-105 transition-all duration-300 group hover:bg-gradient-to-br from-indigo-600 to-purple-600"
				>
					<div
						class="text-4xl font-bold text-white mb-2 group-hover:text-indigo-200 transition-colors duration-300"
					>
						6+
					</div>
					<div class="text-gray-400 group-hover:text-indigo-100">
						Team Members
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Company Story Section -->
	<section class="py-16 bg-gray-900 relative overflow-hidden">
		<div
			class="absolute inset-0 bg-gradient-to-b from-transparent via-indigo-900/10 to-transparent"
		>
		</div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
			<div
				class="lg:text-center mb-12 transform transition-all duration-500 hover:scale-[1.02]"
			>
				<h2
					class="text-base text-indigo-400 font-semibold tracking-wide uppercase animate-fade-in"
				>
					Our Story
				</h2>
				<p
					class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl animate-fade-in-delayed"
				>
					Building the Future of Technology
				</p>
			</div>
			<div class="mt-10">
				<div
					class="prose prose-invert prose-indigo mx-auto text-gray-300 space-y-8"
				>
					<div
						class="bg-gray-800/50 p-6 rounded-xl border border-gray-700 transform hover:scale-[1.01] transition-all duration-300"
					>
						<p class="mb-4">
							Founded in 2015, LugeTech began with a vision to
							transform how businesses interact with technology
							and a goal of producing in-house solutions to
							improve the local tech landscape.
						</p>
						<p class="mb-4">
							Our journey started with a small team of passionate
							developers and has grown into a full-service
							technology solutions provider.
						</p>
						<p>
							Today, we're proud to serve clients across various
							industries, helping them navigate the complex world
							of digital transformation. Our commitment to
							innovation and excellence has made us a trusted
							partner for businesses looking to thrive in the
							digital age.
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Values Section -->
	<section class="py-16 bg-gray-800 relative overflow-hidden">
		<div
			class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-5"
		>
		</div>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
			<div
				class="lg:text-center mb-12 transform transition-all duration-500 hover:scale-[1.02]"
			>
				<h2
					class="text-base text-indigo-400 font-semibold tracking-wide uppercase animate-fade-in"
				>
					Our Values
				</h2>
				<p
					class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl animate-fade-in-delayed"
				>
					What Drives Us
				</p>
			</div>
			<div
				class="mt-10 grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3"
			>
				<div
					class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br from-indigo-600 to-purple-600 border border-gray-600 hover:border-indigo-500"
				>
					<div
						class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-white transition-colors duration-300"
					>
						<svg
							class="w-8 h-8 text-indigo-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 10V3L4 14h7v7l9-11h-7z"></path>
						</svg>
					</div>
					<h3
						class="text-xl font-bold text-white mb-4 group-hover:text-indigo-200"
					>
						Innovation
					</h3>
					<p class="text-gray-300 group-hover:text-indigo-100">
						We constantly push the boundaries of what's possible in
						technology.
					</p>
				</div>
				<div
					class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br from-indigo-600 to-purple-600 border border-gray-600 hover:border-indigo-500"
				>
					<div
						class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-white transition-colors duration-300"
					>
						<svg
							class="w-8 h-8 text-indigo-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
							></path>
						</svg>
					</div>
					<h3
						class="text-xl font-bold text-white mb-4 group-hover:text-indigo-200"
					>
						Excellence
					</h3>
					<p class="text-gray-300 group-hover:text-indigo-100">
						We deliver the highest quality solutions that exceed
						expectations.
					</p>
				</div>
				<div
					class="group bg-gray-700 p-8 rounded-xl shadow-lg transform transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br from-indigo-600 to-purple-600 border border-gray-600 hover:border-indigo-500"
				>
					<div
						class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-white transition-colors duration-300"
					>
						<svg
							class="w-8 h-8 text-indigo-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
							></path>
						</svg>
					</div>
					<h3
						class="text-xl font-bold text-white mb-4 group-hover:text-indigo-200"
					>
						Integrity
					</h3>
					<p class="text-gray-300 group-hover:text-indigo-100">
						We build trust through transparency and honest
						relationships.
					</p>
				</div>
			</div>
		</div>
	</section>
</Layout>

<style>
	@keyframes fade-in {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-fade-in {
		animation: fade-in 0.8s ease-out forwards;
	}

	.animate-fade-in-delayed {
		animation: fade-in 0.8s ease-out 0.2s forwards;
		opacity: 0;
	}
</style>
