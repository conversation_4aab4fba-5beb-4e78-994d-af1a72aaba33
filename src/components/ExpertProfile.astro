---
interface Props {
  name: string;
  role: string;
  image: string;
  bio: string;
  expertise: string[];
  achievements: string[];
  linkedin?: string;
  github?: string;
}

const { name, role, image, bio, expertise, achievements, linkedin, github } = Astro.props;
---

<div class="min-h-screen bg-gray-900 py-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="lg:flex lg:items-center lg:space-x-12">
      <!-- Profile Image Section -->
      <div class="lg:w-1/3">
        <div class="relative h-96 w-full rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 transform transition-all duration-300 hover:scale-105 overflow-hidden shadow-2xl">
          <img src={image} alt={name} class="w-full h-full object-cover opacity-90 hover:opacity-100 transition-opacity duration-300" />
        </div>
      </div>

      <!-- Profile Info Section -->
      <div class="lg:w-2/3 mt-8 lg:mt-0">
        <h1 class="text-4xl font-extrabold text-white mb-2">{name}</h1>
        <p class="text-xl text-indigo-400 mb-6">{role}</p>
        
        <div class="prose prose-invert prose-lg max-w-none mb-8">
          <p class="text-gray-300">{bio}</p>
        </div>

        <!-- Expertise -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-white mb-4">Areas of Expertise</h2>
          <div class="flex flex-wrap gap-3">
            {expertise.map((skill) => (
              <span class="px-4 py-2 bg-indigo-600/20 text-indigo-300 rounded-full text-sm font-medium">
                {skill}
              </span>
            ))}
          </div>
        </div>

        <!-- Achievements -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-white mb-4">Key Achievements</h2>
          <ul class="space-y-3">
            {achievements.map((achievement) => (
              <li class="flex items-start">
                <svg class="w-6 h-6 text-indigo-400 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-gray-300">{achievement}</span>
              </li>
            ))}
          </ul>
        </div>

        <!-- Social Links -->
        <div class="flex space-x-4">
          {linkedin && (
            <a href={linkedin} target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-indigo-400 transition-colors duration-300">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
              </svg>
            </a>
          )}
          {github && (
            <a href={github} target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-indigo-400 transition-colors duration-300">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
          )}
        </div>
      </div>
    </div>
  </div>
</div>