// Utility function to handle scroll animations using Intersection Observer API
export function setupScrollAnimations() {
  // Honor prefers-reduced-motion setting
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  if (prefersReducedMotion) {
    const animatedElements = document.querySelectorAll('.scroll-animate');
    animatedElements.forEach(el => {
      el.classList.add('scroll-visible');
    });
    return;
  }

  // Options for the Intersection Observer
  const options = {
    root: null, // Use the viewport as the root
    rootMargin: '0px', // No margin
    threshold: 0.1 // Trigger when at least 10% of the element is visible
  };

  // Create an observer instance
  const observer = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      // If the element is in view
      if (entry.isIntersecting) {
        // Add the visible class to trigger the animation
        entry.target.classList.add('scroll-visible');
        // Stop observing the element after it's animated
        observer.unobserve(entry.target);
      }
    });
  }, options);

  // Get all elements with the scroll-animate class
  const animatedElements = document.querySelectorAll('.scroll-animate');
  
  // Observe each element
  animatedElements.forEach(element => {
    observer.observe(element);
  });
}
