---
import { ClientRouter } from "astro:transitions";
import { Image } from 'astro:assets';

interface Props {
	title: string;
}

const { title } = Astro.props;
const currentPath = Astro.url.pathname;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta
			name="description"
			content="Lugetech (NOT Logitech) - Guyana's Premier Technology Company. Leading software development, IT services, and digital solutions for Guyanese businesses in Georgetown, New Amsterdam, Linden, and nationwide."
		/>
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />
		<meta name="generator" content={Astro.generator} />

		<!-- Primary Meta Tags -->
		<title>{title} | Lugetech - Premier Tech Company in Guyana</title>
		<meta name="title" content={`${title} | Lugetech - Premier Tech Company in Guyana`} />
		<meta
			name="keywords"
			content="Lugetech Guyana, NOT Logitech, Guyanese tech company, software development Guyana, IT services Georgetown, technology solutions Guyana, web development Guyana, mobile apps Guyana, cloud solutions Caribbean, digital transformation Guyana, tech company Georgetown, Guyana software company, Caribbean technology, South American tech, Demerara tech services, Berbice IT solutions, Essequibo technology"
		/>
		<meta name="author" content="Lugetech" />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={Astro.url.href} />
		<meta property="og:title" content={`${title} | Lugetech - Guyana's Premier Tech Company (Not Logitech)`} />
		<meta
			property="og:description"
			content="Lugetech (NOT Logitech) - Guyana's Premier Technology Company. Leading software development, IT services, and digital solutions for Guyanese businesses in Georgetown, New Amsterdam, Linden, and nationwide."
		/>
		<meta property="og:image" content="/logo-nobg.png" />
		<meta property="og:locale" content="en_GY" />
		<meta property="og:site_name" content="Lugetech" />

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={Astro.url.href} />
		<meta property="twitter:title" content={`${title} | Lugetech - Guyana's Premier Tech Company (Not Logitech)`} />
		<meta
			property="twitter:description"
			content="Lugetech (NOT Logitech) - Guyana's Premier Technology Company. Leading software development, IT services, and digital solutions for Guyanese businesses in Georgetown, New Amsterdam, Linden, and nationwide."
		/>
		<meta property="twitter:image" content="/logo-nobg.png" />
		<meta name="twitter:site" content="@lugetech" />

		<!-- Additional SEO Meta Tags -->
		<meta name="robots" content="index, follow" />
		<meta name="language" content="English" />
		<meta name="revisit-after" content="7 days" />
		<meta name="theme-color" content="#4F46E5" />
		<meta name="geo.region" content="GY" />
		<meta name="geo.country" content="Guyana" />
		<meta name="geo.placename" content="Georgetown, Guyana" />
		<meta name="ICBM" content="6.8013, -58.1553" />
		<meta name="DC.title" content="Lugetech - Guyana's Leading Technology Company (Not Logitech)" />

		<!-- Canonical URL -->
		<link rel="canonical" href={Astro.url.href} />

		<!-- JSON-LD Structured Data -->
		<script type="application/ld+json" is:inline>
			{
				"@context": "https://schema.org",
				"@type": "Organization",
				"name": "Lugetech",
				"alternateName": ["Lugetech Guyana", "Lugetech (Not Logitech)", "Luge Technology"],
				"url": "https://lugetech.com",
				"logo": "https://lugetech.com/logo-nobg.png",
				"description": "Lugetech (NOT Logitech) - Guyana's Premier Technology Company. Leading software development, IT services, and digital solutions for Guyanese businesses in Georgetown, New Amsterdam, Linden, and nationwide.",
				"slogan": "Guyana's Leading Technology Innovation Partner",
				"foundingDate": "2015",
				"areaServed": {
					"@type": "Country",
					"name": "Guyana"
				},
				"knowsAbout": ["Software Development", "Web Development", "Mobile Applications", "Cloud Solutions", "Digital Transformation", "IT Consulting"],
				"founders": [
					{
						"@type": "Person",
						"name": "Ken Taylor"
					},
					{
						"@type": "Person",
						"name": "Andre Blair"
					}
				],
				"address": {
					"@type": "PostalAddress",
					"streetAddress": "123 Tech Street",
					"addressLocality": "Georgetown",
					"addressCountry": "GY",
					"postalCode": "",
					"addressRegion": "Demerara-Mahaica"
				},
				"contactPoint": {
					"@type": "ContactPoint",
					"telephone": "+************",
					"contactType": "customer service",
					"email": "<EMAIL>"
				},
				"sameAs": [
					"https://www.linkedin.com/company/lugetech",
					"https://twitter.com/lugetech",
					"https://github.com/lugetech"
				]
			}
		</script>
		<script type="application/ld+json" is:inline>
			{
				"@context": "https://schema.org",
				"@type": "WebSite",
				"name": "Lugetech",
				"url": "https://lugetech.com",
				"description": "Leading Technology Company in Guyana. We provide cutting-edge software development, IT services, and innovative tech solutions for businesses in Georgetown and across Guyana.",
				"potentialAction": {
					"@type": "SearchAction",
					"target": "https://lugetech.com/search?q={search_term_string}",
					"query-input": "required name=search_term_string"
				},
				"keywords": "Lugetech, Lugetech Guyana, tech company in Guyana, technology solutions, software development, IT services, Georgetown Guyana"
			}
		</script>
		<script type="application/ld+json" is:inline>
			{
				"@context": "https://schema.org",
				"@type": "LocalBusiness",
				"@id": "https://lugetech.com",
				"name": "Lugetech",
				"alternateName": "Lugetech Guyana (Not Logitech)",
				"description": "Guyana's Premier Technology Company specializing in software development, web applications, mobile apps, and IT consulting services.",
				"image": "https://lugetech.com/logo-nobg.png",
				"url": "https://lugetech.com",
				"telephone": "+************",
				"priceRange": "$$",
				"currenciesAccepted": "GYD, USD",
				"paymentAccepted": "Cash, Credit Card, Bank Transfer",
				"serviceArea": {
					"@type": "Country",
					"name": "Guyana"
				},
				"address": {
					"@type": "PostalAddress",
					"streetAddress": "123 Tech Street",
					"addressLocality": "Georgetown",
					"addressRegion": "Demerara-Mahaica",
					"postalCode": "",
					"addressCountry": "GY"
				},
				"geo": {
					"@type": "GeoCoordinates",
					"latitude": 6.8013,
					"longitude": -58.1553
				},
				"openingHoursSpecification": [
					{
						"@type": "OpeningHoursSpecification",
						"dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
						"opens": "09:00",
						"closes": "18:00"
					},
					{
						"@type": "OpeningHoursSpecification",
						"dayOfWeek": "Saturday",
						"opens": "10:00",
						"closes": "14:00"
					}
				],
				"sameAs": [
					"https://www.linkedin.com/company/lugetech",
					"https://twitter.com/lugetech",
					"https://github.com/lugetech"
				]
			}
		</script>

		<ClientRouter fallback="swap" />
		<style is:global>
			::view-transition-old(root),
			::view-transition-new(root) {
				animation-duration: 0.3s;
				animation-timing-function: ease;
			}

			/* Global styles for scroll animations */
			.scroll-animate {
				opacity: 0;
				transform: translateY(30px);
				transition: opacity 0.8s ease, transform 0.8s ease;
				will-change: opacity, transform;
			}

			.scroll-animate.scroll-visible {
				opacity: 1;
				transform: translateY(0);
			}

			/* Modern animations and effects */
			@keyframes fade-in {
				from {
					opacity: 0;
					transform: translateY(20px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			@keyframes float {
				0%, 100% {
					transform: translateY(0px);
				}
				50% {
					transform: translateY(-10px);
				}
			}

			@keyframes pulse-glow {
				0%, 100% {
					box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
				}
				50% {
					box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
				}
			}

			.animate-fade-in {
				animation: fade-in 1s ease-out;
			}

			.animate-float {
				animation: float 3s ease-in-out infinite;
			}

			.animate-pulse-glow {
				animation: pulse-glow 2s ease-in-out infinite;
			}

			/* Glassmorphism effects */
			.glass {
				background: rgba(255, 255, 255, 0.05);
				backdrop-filter: blur(10px);
				border: 1px solid rgba(255, 255, 255, 0.1);
			}

			.glass-dark {
				background: rgba(0, 0, 0, 0.3);
				backdrop-filter: blur(10px);
				border: 1px solid rgba(255, 255, 255, 0.1);
			}

			/* Gradient text */
			.gradient-text {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			/* Hover effects */
			.hover-lift {
				transition: all 0.3s ease;
			}

			.hover-lift:hover {
				transform: translateY(-5px);
				box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			}

			/* Custom scrollbar */
			::-webkit-scrollbar {
				width: 8px;
			}

			::-webkit-scrollbar-track {
				background: #1f2937;
			}

			::-webkit-scrollbar-thumb {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-radius: 4px;
			}

			::-webkit-scrollbar-thumb:hover {
				background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
			}

			html {
				scroll-behavior: smooth;
			}

			/* Mobile optimizations */
			@media (max-width: 768px) {
				.mobile-padding {
					padding-left: 1rem;
					padding-right: 1rem;
				}
			}
		</style>
	</head>
	<body class="min-h-screen bg-gray-900">
		<nav class="fixed top-0 left-0 right-0 z-50 bg-gray-900/80 backdrop-blur-md border-b border-gray-800/50 shadow-lg">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between h-20">
					<div class="flex items-center">
						<a
							href="/"
							class="flex items-center space-x-3 group"
						>
							<img
								src="/logo-nobg-white-stroke.png"
								alt="Lugetech Logo"
								class="h-12 w-12 transition-transform duration-300 group-hover:scale-110"
							/>
							<span class="text-xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
								Lugetech
							</span>
						</a>
					</div>
					
					<div class="hidden sm:flex items-center space-x-1">
						{[
							{ href: "/", label: "Home" },
							{ href: "/about", label: "About" },
							{ href: "/services", label: "Services" },
							{ href: "/projects", label: "Projects" },
							{ href: "/contact", label: "Contact" }
						].map(({ href, label }) => (
							<a
								href={href}
								class={`relative px-4 py-2 text-sm font-medium transition-all duration-300 rounded-lg ${
									currentPath === href
										? "text-white bg-indigo-600/20"
										: "text-gray-300 hover:text-white hover:bg-gray-800/50"
								}`}
							>
								{label}
								{currentPath === href && (
									<span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-indigo-400 rounded-full"></span>
								)}
							</a>
						))}
					</div>
					
					<div class="sm:hidden flex items-center">
						<button
							type="button"
							id="mobile-menu-button"
							class="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-300"
							aria-expanded="false"
						>
							<span class="sr-only">Open main menu</span>
							<svg
								class="block h-6 w-6"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M4 6h16M4 12h16M4 18h16"></path>
							</svg>
						</button>
					</div>
				</div>
			</div>

			<div class="sm:hidden hidden bg-gray-900/95 backdrop-blur-lg border-t border-gray-800/50" id="mobile-menu">
				<div class="px-4 py-4 space-y-1">
					{[
						{ href: "/", label: "Home" },
						{ href: "/about", label: "About" },
						{ href: "/services", label: "Services" },
						{ href: "/projects", label: "Projects" },
						{ href: "/contact", label: "Contact" }
					].map(({ href, label }) => (
						<a
							href={href}
							class={`block px-4 py-3 text-base font-medium rounded-xl transition-all duration-300 transform hover:scale-105 ${
								currentPath === href
									? "text-white bg-gradient-to-r from-indigo-600/20 to-purple-600/20 border border-indigo-500/30"
									: "text-gray-300 hover:text-white hover:bg-gray-800/50 border border-transparent"
							}`}
						>
							{label}
						</a>
					))}
				</div>
			</div>
		</nav>

		<main>
			<slot />
		</main>

		<footer class="bg-gradient-to-b from-gray-900 to-black border-t border-gray-800/50">
			<div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
				<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
					<div>
						<div class="flex items-center space-x-3 mb-4">
							<img
								src="/logo-nobg-white-stroke.png"
								alt="Lugetech Logo"
								class="h-10 w-10"
							/>
							<span class="text-xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
								Lugetech
							</span>
						</div>
						<p class="text-gray-400 leading-relaxed">
							Innovative technology solutions for modern businesses in Guyana and beyond.
						</p>
					</div>
					<div>
						<h3 class="text-lg font-semibold mb-4">Quick Links</h3>
						<ul class="space-y-2">
							<li>
								<a
									href="/"
									class="text-gray-300 hover:text-white"
									>Home</a
								>
							</li>
							<li>
								<a
									href="/about"
									class="text-gray-300 hover:text-white"
									>About</a
								>
							</li>
							<li>
								<a
									href="/services"
									class="text-gray-300 hover:text-white"
									>Services</a
								>
							</li>
							<li>
								<a
									href="/contact"
									class="text-gray-300 hover:text-white"
									>Contact</a
								>
							</li>
							<li>
								<a
									href="/projects"
									class="text-gray-300 hover:text-white"
									>Projects</a
								>
							</li>
						</ul>
					</div>
					<div>
						<h3 class="text-lg font-semibold mb-4">Contact</h3>
						<ul class="space-y-2 text-gray-300">
							<li>123 Tech Street</li>
							<li>Georgetown Guyana</li>
							<li><EMAIL></li>
							<li>(592) 654 7080</li>
						</ul>
					</div>
					<div>
						<h3 class="text-lg font-semibold mb-4">Follow Us</h3>
						<div class="flex space-x-4">
							<a href="#" class="text-gray-300 hover:text-white"
								>LinkedIn</a
							>
							<a href="#" class="text-gray-300 hover:text-white"
								>Twitter</a
							>
							<a href="#" class="text-gray-300 hover:text-white"
								>GitHub</a
							>
						</div>
					</div>
				</div>
				<div
					class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"
				>
					<p>
						&copy; {new Date().getFullYear()} Lugetech. All rights reserved.
					</p>
				</div>
			</div>
		</footer>
	</body>
</html>

<script>
	import { setupScrollAnimations } from '../utils/scrollAnimation';

	document.addEventListener("astro:page-load", () => {
		// Mobile menu toggle
		const mobileMenuButton = document.getElementById("mobile-menu-button");
		const mobileMenu = document.getElementById("mobile-menu");

		mobileMenuButton?.addEventListener("click", () => {
			const isExpanded =
				mobileMenuButton.getAttribute("aria-expanded") === "true";
			mobileMenuButton.setAttribute("aria-expanded", String(!isExpanded));
			mobileMenu?.classList.toggle("hidden");
		});

		// Initialize scroll animations
		setupScrollAnimations();
	});
</script>
